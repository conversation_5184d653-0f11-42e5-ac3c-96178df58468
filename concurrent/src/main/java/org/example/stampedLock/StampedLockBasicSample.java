package org.example.stampedLock;

import java.util.concurrent.locks.StampedLock;

/**
 * TODO
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/12/2
 **/
public class StampedLockBasicSample {

    public static class Point {
        private double x, y;
        private final StampedLock sl = new StampedLock();

        public void move(double deltaX, double deltaY) {
            long stamp = sl.writeLock();
            try {
                x = deltaX;
                y = deltaY;
            } finally {
                sl.unlockWrite(stamp);
            }
        }

        public double distanceFromOrigin() {
            long stamp = sl.tryOptimisticRead();
            // 拷贝共享资源到本地方法栈中，确保多数据的原子性
            double currentX = x, currentY = y;
            if (!sl.validate(stamp)) {
                stamp = sl.readLock();
                try {
                    currentX = x;
                    currentY = y;
                } finally {
                    sl.unlockRead(stamp);
                }
            }
            return Math.sqrt(currentX * currentX + currentY * currentY);
        }

        void moveIfAtOrigin(double newX, double newY) { // upgrade
            // Could instead start with optimistic, not read mode
            long stamp = sl.readLock();
            try {
                while (x == 0.0 && y == 0.0) {
                    long ws = sl.tryConvertToWriteLock(stamp);  //读锁转换为写锁
                    if (ws != 0L) {
                        stamp = ws;
                        x = newX;
                        y = newY;
                        break;
                    } else {
                        sl.unlockRead(stamp);
                        stamp = sl.writeLock();
                    }
                }
            } finally {
                sl.unlock(stamp);
            }
        }

    }
}
