package org.example.linkHashMap_keyset;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;

/**
 * <p>文件名称:org.example.linkHashMap_keyset.LinkHashMapKeyset</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2022/7/14
 */
public class LinkHashMapKeySet {


    public static void main(String[] args) {

        for (int i=0; i< 10000000; ++i){
            LinkedHashMap<Integer, Integer> res = new LinkedHashMap<>();
            res.put(1, 1);
            res.put(2, 2);
            res.put(3, 3);
            res.put(4, 4);
            res.put(5, 5);
            res.put(7, 7);
            res.put(6, 6);
            LinkedHashSet<Integer> set = new LinkedHashSet<>();
            set.addAll(Arrays.asList(1, 2, 3, 4, 5, 7, 6));
            if (!set.equals(res.keySet())) {
                System.out.println(res.keySet());
            }

        }
    }
}
