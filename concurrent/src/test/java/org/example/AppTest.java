package org.example;

import org.junit.Test;

import java.math.BigDecimal;

/**
 * Unit test for simple App.
 */
public class AppTest {
    /**
     * Rigorous Test :-)
     */
    @Test
    public void shouldAnswerWithTrue() {
        BigDecimal t1 = new BigDecimal("20.1");

        BigDecimal t2 = new BigDecimal("10");
        BigDecimal t3 = new BigDecimal("30");

        BigDecimal res = t1.multiply(t2.divide(t3, 2));
        System.out.println(res);
    }

    public static void main(String[] args) {
        long st = System.currentTimeMillis();
        for (int i = 0; i < 100; ++i) {
            for (int j = 0; j < 1000; ++j) {
                for (int k = 0; k < 10000; ++k) {
                }
            }
        }
        System.out.println(System.currentTimeMillis() - st);

        st = System.currentTimeMillis();
        for (int i = 0; i < 10000; ++i) {
            for (int j = 0; j < 1000; ++j) {
                for (int k = 0; k < 100; ++k) {
                }
            }
        }
        System.out.println(System.currentTimeMillis() - st);

        int[] arr = new int[64 * 1024 * 1024];
        st = System.currentTimeMillis();
        for (int i=0; i< arr.length; ++i) arr[i] *= 3;
        System.out.println(System.currentTimeMillis() - st);

        st = System.currentTimeMillis();
        for (int i=0; i< arr.length; i += 16) arr[i] *= 3;
        System.out.println(System.currentTimeMillis() - st);
    }
}
