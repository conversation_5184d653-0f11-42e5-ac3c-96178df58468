package org.example;

import com.google.common.util.concurrent.RateLimiter;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Hello world!
 *
 */
public class BasicTest {

    public static void main( String[] args ) {
        RateLimiter rateLimiter = RateLimiter.create(3.0d);

        ExecutorService es = Executors.newSingleThreadExecutor();
        AtomicLong prev = new AtomicLong(System.nanoTime());
        for (int i = 0; i<20; ++i) {
            rateLimiter.acquire();
            es.execute(() -> {
                long now = System.nanoTime();
                System.out.println((now - prev.get())/ 1000_000);
                prev.set(now);
            });
        }
    }
}
