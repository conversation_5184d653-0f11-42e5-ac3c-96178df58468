package org.example;

import java.util.concurrent.TimeUnit;

public class SimpleLimiter {

    long next = System.nanoTime();
    long internal = 1000_000_000;

    // 预占令牌
    synchronized long reserve(long now) {
        if (now > next) {
            next = now;
        }
        long at = next;
        next += internal;
        return Math.max(at, 0L);
    }

    void acquire() {
        long now = System.nanoTime();
        long at = reserve(now);
        long waitTime = Math.max(at - now, 0);

        if (waitTime > 0) {
            try {
                TimeUnit.NANOSECONDS.sleep(waitTime);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }
}
