#!/bin/bash

echo "=== GIF to Binary Converter 测试脚本 ==="

# 构建项目
echo "1. 构建项目..."
mvn package -q
if [ $? -ne 0 ]; then
    echo "构建失败!"
    exit 1
fi

echo "构建成功!"

# 测试帮助功能
echo ""
echo "2. 测试帮助功能..."
java -jar target/img2bin-1.0-SNAPSHOT.jar --help

# 查找测试用的GIF文件
echo ""
echo "3. 查找测试用的GIF文件..."
TEST_GIF=$(find ~/Downloads -name "*.gif" -type f | head -1)

if [ -z "$TEST_GIF" ]; then
    echo "未找到测试用的GIF文件"
    echo "请将一个GIF文件放到Downloads目录中进行测试"
    exit 1
fi

echo "找到测试文件: $TEST_GIF"

# 测试转换功能
echo ""
echo "4. 测试转换功能..."
java -jar target/img2bin-1.0-SNAPSHOT.jar "$TEST_GIF" -w 64 -h 64

if [ $? -eq 0 ]; then
    echo ""
    echo "转换成功!"
    
    # 显示生成的文件信息
    OUTPUT_FILE=$(basename "$TEST_GIF" .gif).bin
    if [ -f "$OUTPUT_FILE" ]; then
        echo "生成的文件: $OUTPUT_FILE"
        echo "文件大小: $(ls -lh "$OUTPUT_FILE" | awk '{print $5}')"
        echo "文件头部信息:"
        hexdump -C "$OUTPUT_FILE" | head -3
    fi
else
    echo "转换失败，可能是GIF文件格式不兼容"
fi

echo ""
echo "=== 测试完成 ==="
