# GIF to Binary Converter (Java版本)

这是一个将GIF文件转换为RGB565格式二进制文件的Java工具，主要用于嵌入式设备显示。

## 功能特性

- 将GIF动画转换为RGB565格式的二进制文件
- 支持调整输出图像尺寸
- 支持限制最大帧数（帧降采样）
- 生成带帧索引结构的二进制文件
- 支持RGB565A8格式（RGB565 + Alpha通道）

## 构建项目

```bash
cd img2bin
mvn clean package
```

构建完成后，可执行JAR文件位于 `target/img2bin-1.0-SNAPSHOT.jar`

## 使用方法

### 基本用法

```bash
java -jar target/img2bin-1.0-SNAPSHOT.jar input.gif
```

### 指定输出尺寸

```bash
java -jar target/img2bin-1.0-SNAPSHOT.jar input.gif -w 128 -h 128
```

### 限制最大帧数

```bash
java -jar target/img2bin-1.0-SNAPSHOT.jar input.gif -m 30
```

### 完整参数示例

```bash
java -jar target/img2bin-1.0-SNAPSHOT.jar input.gif -w 64 -h 64 -m 20
```

### 查看帮助

```bash
java -jar target/img2bin-1.0-SNAPSHOT.jar --help
```

## 命令行参数

- `-w, --width <arg>`: 输出宽度（像素）
- `-h, --height <arg>`: 输出高度（像素）  
- `-m, --max-frames <arg>`: 最大帧数（可选，用于帧降采样）
- `--help`: 显示帮助信息

## 输出格式

生成的二进制文件结构：

```
[头部信息 - 12字节]
- 总帧数 (4字节，小端序)
- 图像宽度 (4字节，小端序)
- 图像高度 (4字节，小端序)

[帧索引 - 每帧8字节]
- 帧数据起始位置 (4字节，小端序)
- 帧数据长度 (4字节，小端序)

[帧数据]
- RGB565A8格式的图像数据 (每像素3字节：RGB565 + Alpha)
```

## 图像格式说明

- **RGB565**: 16位颜色格式，5位红色 + 6位绿色 + 5位蓝色
- **RGB565A8**: RGB565 + 8位Alpha通道，每像素3字节
- **小端序**: 多字节数据以小端序存储

## 示例

假设有一个名为 `animation.gif` 的文件：

```bash
# 转换为64x64像素，最多20帧
java -jar target/img2bin-1.0-SNAPSHOT.jar animation.gif -w 64 -h 64 -m 20
```

输出文件：`animation.bin`

程序会显示：
```
原始帧数: 45
输出帧数: 20
转换完成 → animation.bin
输出大小: 245.76 KB
```

## 技术细节

- 使用Java标准库的ImageIO处理GIF文件
- 支持透明度处理（Alpha通道）
- 自动进行帧降采样以控制文件大小
- 生成的二进制文件可直接用于嵌入式设备
