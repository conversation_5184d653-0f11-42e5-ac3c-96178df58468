package org.example;

import javax.imageio.ImageIO;
import javax.imageio.ImageReader;
import javax.imageio.stream.ImageInputStream;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.DataOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;

/**
 * <p>文件名称:org.example.Image2bin</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2025/7/8
 */
public class Image2binUtil {

    public static void gif2Bin(File gitFile, String outputBin, Integer width, Integer height, Integer maxFrames) throws IOException {
        List<BufferedImage> frames = readGifFrames(gitFile);

        System.out.println("原始帧数: " + frames.size());

        if (Objects.isNull(width) || Objects.isNull(height)) {
            BufferedImage firstFrame = frames.get(0);
            width = firstFrame.getWidth();
            height = firstFrame.getHeight();
        } else {
            for (int i = 0; i < frames.size(); i++) {
                frames.set(i, resizeImage(frames.get(i), width, height));
            }
        }

        // 控制最大帧数（帧降采样）
        if (!Objects.isNull(maxFrames) && frames.size() > maxFrames) {
            int skipFactor = frames.size() / maxFrames + 1;
            List<BufferedImage> sampledFrames = new ArrayList<>();
            for (int i = 0; i < frames.size(); i += skipFactor) {
                sampledFrames.add(frames.get(i));
            }
            frames = sampledFrames;
        }

        System.out.println("输出帧数: " + frames.size());

        try (DataOutputStream dos = new DataOutputStream(Files.newOutputStream(Paths.get(outputBin)))) {
            /*Tip：设备端（宇敏）定制的头信息，皆使用小端序：
                总帧数(4),
                分辨率-宽(4),
                分辨率-高(4),
                每数据帧的起始位置和大小(8),
                数据帧
            */
            dos.writeInt(Integer.reverseBytes(frames.size()));
            dos.writeInt(Integer.reverseBytes(width));
            dos.writeInt(Integer.reverseBytes(height));

            int dataOffset = 12 + frames.size() * 8;
            // 准备帧数据
            List<byte[]> frameDataList = new ArrayList<>();
            for (BufferedImage frame : frames) {
                byte[] frameData = convertToRgb565a8(frame);
                frameDataList.add(frameData);

                // 写入帧索引：起始位置和长度
                dos.writeInt(Integer.reverseBytes(dataOffset));
                dos.writeInt(Integer.reverseBytes(frameData.length));
                dataOffset += frameData.length;
            }

            // 写入所有帧数据
            for (byte[] frameData : frameDataList) {
                dos.write(frameData);
            }
        }

        System.out.println("转换完成 → " + outputBin);
        File outputFile = new File(outputBin);
        System.out.printf("输出大小: %.2f KB%n", outputFile.length() / 1024.0);
    }

    private static BufferedImage resizeImage(BufferedImage original, int width, int height) {
        BufferedImage resized = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = resized.createGraphics();
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        g2d.drawImage(original, 0, 0, width, height, null);
        g2d.dispose();
        return resized;
    }

    public static List<BufferedImage> readGifFrames(File gitFile) throws IOException {
        List<BufferedImage> frames = new ArrayList<>();

        try (ImageInputStream input = ImageIO.createImageInputStream(gitFile)) {
            Iterator<ImageReader> readers = ImageIO.getImageReadersByFormatName("gif");
            if (!readers.hasNext()) {
                throw new IOException("No GIF readers found");
            }

            ImageReader reader = readers.next();
            reader.setInput(input);

            try {
                int numFrames;
                try {
                    numFrames = reader.getNumImages(true);
                } catch (Exception e) {
                    // 如果无法获取准确帧数，尝试逐帧读取
                    System.out.println("警告: 无法获取准确帧数，尝试逐帧读取...");
                    numFrames = Integer.MAX_VALUE;
                }

                for (int i = 0; i < numFrames; i++) {
                    try {
                        BufferedImage frame = reader.read(i);
                        if (frame != null) {
                            // 确保图像格式一致，转换为ARGB
                            BufferedImage convertedFrame = new BufferedImage(
                                    frame.getWidth(), frame.getHeight(), BufferedImage.TYPE_INT_ARGB);
                            Graphics2D g2d = convertedFrame.createGraphics();
                            g2d.drawImage(frame, 0, 0, null);
                            g2d.dispose();
                            frames.add(convertedFrame);
                        }
                    } catch (IndexOutOfBoundsException e) {
                        // 到达文件末尾，正常退出
                        System.out.println("读取到第 " + i + " 帧时到达文件末尾");
                        break;
                    } catch (Exception e) {
                        System.err.println("跳过损坏的第 " + i + " 帧: " + e.getMessage());
                        continue;
                    }
                }
            } finally {
                reader.dispose();
            }
        }

        if (frames.isEmpty()) {
            throw new IOException("无法从GIF文件中读取任何有效帧");
        }

        return frames;
    }

    /**
     * 3 Bytes，RGB565 + A8 +alpha通道
     */
    private static byte[] convertToRgb565a8(BufferedImage img) {
        int width = img.getWidth();
        int height = img.getHeight();
        ByteBuffer buffer = ByteBuffer.allocate(width * height * 3);
        buffer.order(ByteOrder.LITTLE_ENDIAN); //小端序
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int argb = img.getRGB(x, y);
                int a = (argb >> 24) & 0xFF;
                int r = (argb >> 16) & 0xFF;
                int g = (argb >> 8) & 0xFF;
                int b = argb & 0xFF;

                int pixel = ((r & 0xF8) << 8) | ((g & 0xFC) << 3) | (b >> 3);  //RGB565
                buffer.putShort((short) pixel);
                buffer.put((byte) a); //Alpha
            }
        }
        return buffer.array();
    }


}
