package org.example;

import java.io.IOException;

/**
 * GIF转换工具使用示例
 * 演示如何使用新的API获取字节流数据并进行后续处理
 */
public class Gif2binExample {

    /**
     * 示例1：获取字节流数据并写入文件
     */
    public static void example1_BasicUsage(String gifPath, String outputPath) throws IOException {
        System.out.println("=== 示例1：基本用法 ===");
        
        // 获取二进制数据
        byte[] binData = Gif2bin.gifToBinData(gifPath);
        
        // 外层处理数据（这里可以进行任何自定义处理）
        System.out.println("获取到二进制数据，长度: " + binData.length + " 字节");
        
        // 写入文件
        Gif2bin.writeBinDataToFile(binData, outputPath);
    }

    /**
     * 示例2：指定尺寸转换
     */
    public static void example2_WithSize(String gifPath, String outputPath, int width, int height) throws IOException {
        System.out.println("=== 示例2：指定尺寸转换 ===");
        
        // 获取指定尺寸的二进制数据
        byte[] binData = Gif2bin.gifToBinData(gifPath, width, height);
        
        // 可以在这里对数据进行处理
        System.out.println("转换为 " + width + "x" + height + " 尺寸");
        System.out.println("数据大小: " + binData.length + " 字节");
        
        // 写入文件
        Gif2bin.writeBinDataToFile(binData, outputPath);
    }

    /**
     * 示例3：限制帧数并进行自定义处理
     */
    public static void example3_WithFrameLimit(String gifPath, String outputPath, int width, int height, int maxFrames) throws IOException {
        System.out.println("=== 示例3：限制帧数并自定义处理 ===");
        
        // 获取限制帧数的二进制数据
        byte[] binData = Gif2bin.gifToBinData(gifPath, width, height, maxFrames);
        
        // 自定义处理：分析数据头部
        analyzeHeader(binData);
        
        // 可以进行其他处理，比如压缩、加密等
        byte[] processedData = customProcessing(binData);
        
        // 写入处理后的数据
        Gif2bin.writeBinDataToFile(processedData, outputPath);
    }

    /**
     * 示例4：批量处理多个文件
     */
    public static void example4_BatchProcessing(String[] gifPaths, String outputDir) throws IOException {
        System.out.println("=== 示例4：批量处理 ===");
        
        for (int i = 0; i < gifPaths.length; i++) {
            String gifPath = gifPaths[i];
            String outputPath = outputDir + "/output_" + i + ".bin";
            
            try {
                // 获取数据
                byte[] binData = Gif2bin.gifToBinData(gifPath, 64, 64, 20);
                
                // 批量处理逻辑
                System.out.println("处理文件 " + (i + 1) + "/" + gifPaths.length + ": " + gifPath);
                
                // 写入文件
                Gif2bin.writeBinDataToFile(binData, outputPath);
                
            } catch (IOException e) {
                System.err.println("处理文件失败: " + gifPath + " - " + e.getMessage());
            }
        }
    }

    /**
     * 分析二进制数据头部信息
     */
    private static void analyzeHeader(byte[] binData) {
        if (binData.length < 12) {
            System.out.println("数据太短，无法分析头部");
            return;
        }
        
        // 解析头部信息（小端序）
        int frameCount = (binData[3] & 0xFF) << 24 | (binData[2] & 0xFF) << 16 | 
                        (binData[1] & 0xFF) << 8 | (binData[0] & 0xFF);
        int width = (binData[7] & 0xFF) << 24 | (binData[6] & 0xFF) << 16 | 
                   (binData[5] & 0xFF) << 8 | (binData[4] & 0xFF);
        int height = (binData[11] & 0xFF) << 24 | (binData[10] & 0xFF) << 16 | 
                    (binData[9] & 0xFF) << 8 | (binData[8] & 0xFF);
        
        System.out.println("头部信息分析:");
        System.out.println("  帧数: " + frameCount);
        System.out.println("  宽度: " + width);
        System.out.println("  高度: " + height);
        System.out.println("  预期数据大小: " + (width * height * frameCount * 3) + " 字节");
    }

    /**
     * 自定义数据处理（示例：简单的数据验证）
     */
    private static byte[] customProcessing(byte[] binData) {
        System.out.println("执行自定义处理...");
        
        // 这里可以进行各种自定义处理
        // 例如：数据压缩、加密、格式转换等
        
        // 示例：添加简单的校验和
        byte[] processedData = new byte[binData.length + 4];
        System.arraycopy(binData, 0, processedData, 0, binData.length);
        
        // 计算简单校验和
        int checksum = 0;
        for (byte b : binData) {
            checksum += b & 0xFF;
        }
        
        // 添加校验和到末尾（小端序）
        processedData[binData.length] = (byte) (checksum & 0xFF);
        processedData[binData.length + 1] = (byte) ((checksum >> 8) & 0xFF);
        processedData[binData.length + 2] = (byte) ((checksum >> 16) & 0xFF);
        processedData[binData.length + 3] = (byte) ((checksum >> 24) & 0xFF);
        
        System.out.println("添加校验和: " + checksum);
        
        return processedData;
    }

    /**
     * 主方法：演示各种用法
     */
    public static void main(String[] args) {
        if (args.length < 1) {
            System.out.println("用法: java Gif2binExample <gif文件路径>");
            return;
        }
        
        String gifPath = args[0];
        
        try {
            // 示例1：基本用法
            example1_BasicUsage(gifPath, "output1.bin");
            
            // 示例2：指定尺寸
            example2_WithSize(gifPath, "output2.bin", 64, 64);
            
            // 示例3：限制帧数
            example3_WithFrameLimit(gifPath, "output3.bin", 32, 32, 10);
            
            System.out.println("\n所有示例执行完成！");
            
        } catch (IOException e) {
            System.err.println("处理过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
