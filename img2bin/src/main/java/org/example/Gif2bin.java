package org.example;

//import org.apache.commons.cli.*;
//
//import org.apache.commons.cli.CommandLine;
//import org.apache.commons.cli.CommandLineParser;

import org.apache.commons.cli.CommandLine;
import org.apache.commons.cli.CommandLineParser;

import javax.imageio.ImageIO;
import javax.imageio.ImageReader;
import javax.imageio.stream.ImageInputStream;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * 将GIF文件转换为带帧索引结构的RGB565格式bin文件
 */
public class Gif2bin {

    /**
     * 将BufferedImage转换为RGB565格式字节流（小端序）
     * 2字节，RGB565，不带alpha通道
     */
    public static byte[] convertToRgb565(BufferedImage img) {
        int width = img.getWidth();
        int height = img.getHeight();
        ByteBuffer buffer = ByteBuffer.allocate(width * height * 2);
        buffer.order(ByteOrder.LITTLE_ENDIAN);

        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int rgb = img.getRGB(x, y);
                int r = (rgb >> 16) & 0xFF;
                int g = (rgb >> 8) & 0xFF;
                int b = rgb & 0xFF;

                // 转换为RGB565格式
                int pixel = ((r & 0xF8) << 8) | ((g & 0xFC) << 3) | (b >> 3);
                buffer.putShort((short) pixel);
            }
        }

        return buffer.array();
    }

    /**
     * 将BufferedImage转换为RGB565A8格式字节流
     * 3字节，RGB565 + A8，带alpha通道
     */
    public static byte[] convertToRgb565a8(BufferedImage img) {
        int width = img.getWidth();
        int height = img.getHeight();
        ByteBuffer buffer = ByteBuffer.allocate(width * height * 3);
        buffer.order(ByteOrder.LITTLE_ENDIAN);

        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int argb = img.getRGB(x, y);
                int a = (argb >> 24) & 0xFF;
                int r = (argb >> 16) & 0xFF;
                int g = (argb >> 8) & 0xFF;
                int b = argb & 0xFF;

                // RGB565部分
                int pixel = ((r & 0xF8) << 8) | ((g & 0xFC) << 3) | (b >> 3);
                buffer.putShort((short) pixel);
                // Alpha部分
                buffer.put((byte) a);
            }
        }

        return buffer.array();
    }

    /**
     * 将BufferedImage转换为ARGB8888格式字节流
     * 4字节，RGBA，带alpha通道
     */
    public static byte[] convertToArgb8888(BufferedImage img) {
        int width = img.getWidth();
        int height = img.getHeight();
        byte[] data = new byte[width * height * 4];
        int index = 0;

        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int argb = img.getRGB(x, y);
                int a = (argb >> 24) & 0xFF;
                int r = (argb >> 16) & 0xFF;
                int g = (argb >> 8) & 0xFF;
                int b = argb & 0xFF;

                // BGRA顺序（小端序）
                data[index++] = (byte) b;
                data[index++] = (byte) g;
                data[index++] = (byte) r;
                data[index++] = (byte) a;
            }
        }

        return data;
    }

    /**
     * 读取GIF文件的所有帧
     */
    public static List<BufferedImage> readGifFrames(String gifPath) throws IOException {
        List<BufferedImage> frames = new ArrayList<>();

        try (ImageInputStream input = ImageIO.createImageInputStream(new File(gifPath))) {
            Iterator<ImageReader> readers = ImageIO.getImageReadersByFormatName("gif");
            if (!readers.hasNext()) {
                throw new IOException("No GIF readers found");
            }

            ImageReader reader = readers.next();
            reader.setInput(input);

            try {
                int numFrames;
                try {
                    numFrames = reader.getNumImages(true);
                } catch (Exception e) {
                    // 如果无法获取准确帧数，尝试逐帧读取
                    System.out.println("警告: 无法获取准确帧数，尝试逐帧读取...");
                    numFrames = Integer.MAX_VALUE;
                }

                for (int i = 0; i < numFrames; i++) {
                    try {
                        BufferedImage frame = reader.read(i);
                        if (frame != null) {
                            // 确保图像格式一致，转换为ARGB
                            BufferedImage convertedFrame = new BufferedImage(
                                frame.getWidth(), frame.getHeight(), BufferedImage.TYPE_INT_ARGB);
                            Graphics2D g2d = convertedFrame.createGraphics();
                            g2d.drawImage(frame, 0, 0, null);
                            g2d.dispose();
                            frames.add(convertedFrame);
                        }
                    } catch (IndexOutOfBoundsException e) {
                        // 到达文件末尾，正常退出
                        System.out.println("读取到第 " + i + " 帧时到达文件末尾");
                        break;
                    } catch (Exception e) {
                        System.err.println("跳过损坏的第 " + i + " 帧: " + e.getMessage());
                        continue;
                    }
                }
            } finally {
                reader.dispose();
            }
        }

        if (frames.isEmpty()) {
            throw new IOException("无法从GIF文件中读取任何有效帧");
        }

        return frames;
    }

    /**
     * 备用GIF读取方法 - 使用更宽松的设置
     */
    public static List<BufferedImage> readGifFramesSimple(String gifPath) throws IOException {
        List<BufferedImage> frames = new ArrayList<>();

        try (ImageInputStream input = ImageIO.createImageInputStream(new File(gifPath))) {
            Iterator<ImageReader> readers = ImageIO.getImageReadersByFormatName("gif");
            if (!readers.hasNext()) {
                throw new IOException("No GIF readers found");
            }

            ImageReader reader = readers.next();
            reader.setInput(input, false, true); // 设置为忽略元数据

            try {
                // 只尝试读取第一帧
                BufferedImage frame = reader.read(0);
                if (frame != null) {
                    // 转换为ARGB格式
                    BufferedImage convertedFrame = new BufferedImage(
                        frame.getWidth(), frame.getHeight(), BufferedImage.TYPE_INT_ARGB);
                    Graphics2D g2d = convertedFrame.createGraphics();
                    g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
                    g2d.drawImage(frame, 0, 0, null);
                    g2d.dispose();
                    frames.add(convertedFrame);
                    System.out.println("使用简单模式，成功读取第一帧 (" + frame.getWidth() + "x" + frame.getHeight() + ")");
                }
            } catch (Exception e) {
                System.err.println("简单模式读取失败: " + e.getMessage());
                // 最后的备用方案：使用ImageIO.read
                try {
                    BufferedImage image = ImageIO.read(new File(gifPath));
                    if (image != null) {
                        BufferedImage convertedFrame = new BufferedImage(
                            image.getWidth(), image.getHeight(), BufferedImage.TYPE_INT_ARGB);
                        Graphics2D g2d = convertedFrame.createGraphics();
                        g2d.drawImage(image, 0, 0, null);
                        g2d.dispose();
                        frames.add(convertedFrame);
                        System.out.println("使用最基本的读取方式成功");
                    }
                } catch (Exception e2) {
                    throw new IOException("所有读取方式都失败了: " + e2.getMessage(), e2);
                }
            } finally {
                reader.dispose();
            }
        }

        if (frames.isEmpty()) {
            throw new IOException("无法从文件中读取任何图像数据");
        }

        return frames;
    }

    /**
     * 调整图像大小
     */
    public static BufferedImage resizeImage(BufferedImage original, int width, int height) {
        BufferedImage resized = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = resized.createGraphics();
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        g2d.drawImage(original, 0, 0, width, height, null);
        g2d.dispose();
        return resized;
    }

    /**
     * 将GIF转换为带帧索引结构的RGB565二进制数据
     * @param gifPath GIF文件路径
     * @param width 输出宽度（可为null使用原始宽度）
     * @param height 输出高度（可为null使用原始高度）
     * @param maxFrames 最大帧数（可为null不限制）
     * @return 二进制数据字节数组
     * @throws IOException 处理异常
     */
    public static byte[] gifToBinData(String gifPath, Integer width, Integer height, Integer maxFrames)
            throws IOException {

        List<BufferedImage> frames;
        try {
            frames = readGifFrames(gifPath);
        } catch (Exception e) {
            System.err.println("多帧读取失败: " + e.getMessage());
            System.out.println("尝试使用简单模式读取...");
            frames = readGifFramesSimple(gifPath);
        }

        System.out.println("原始帧数: " + frames.size());

        // 调整图像大小
        if (width != null && height != null) {
            for (int i = 0; i < frames.size(); i++) {
                frames.set(i, resizeImage(frames.get(i), width, height));
            }
        } else {
            // 使用第一帧的尺寸
            BufferedImage firstFrame = frames.get(0);
            width = firstFrame.getWidth();
            height = firstFrame.getHeight();
        }

        // 控制最大帧数（帧降采样）
        if (maxFrames != null && frames.size() > maxFrames) {
            int skipFactor = frames.size() / maxFrames + 1;
            List<BufferedImage> sampledFrames = new ArrayList<>();
            for (int i = 0; i < frames.size(); i += skipFactor) {
                sampledFrames.add(frames.get(i));
            }
            frames = sampledFrames;
        }

        System.out.println("输出帧数: " + frames.size());

        // 使用ByteArrayOutputStream来构建二进制数据
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream();
             DataOutputStream dos = new DataOutputStream(baos)) {

            // 写入头信息
            dos.writeInt(Integer.reverseBytes(frames.size())); // 总帧数（小端序）
            dos.writeInt(Integer.reverseBytes(width));         // 宽度（小端序）
            dos.writeInt(Integer.reverseBytes(height));        // 高度（小端序）

            // 计算数据偏移量：12字节头 + 每帧8字节索引
            int dataOffset = 12 + frames.size() * 8;

            // 准备帧数据
            List<byte[]> frameDataList = new ArrayList<>();
            for (BufferedImage frame : frames) {
                byte[] frameData = convertToRgb565a8(frame);
                frameDataList.add(frameData);

                // 写入帧索引：起始位置和长度
                dos.writeInt(Integer.reverseBytes(dataOffset));
                dos.writeInt(Integer.reverseBytes(frameData.length));
                dataOffset += frameData.length;
            }

            // 写入所有帧数据
            for (byte[] frameData : frameDataList) {
                dos.write(frameData);
            }

            dos.flush();
            byte[] result = baos.toByteArray();
            System.out.printf("生成二进制数据大小: %.2f KB%n", result.length / 1024.0);
            return result;
        }
    }

    /**
     * 将GIF转换为带帧索引结构的RGB565 bin文件
     * @param gifPath GIF文件路径
     * @param outputBin 输出文件路径
     * @param width 输出宽度（可为null使用原始宽度）
     * @param height 输出高度（可为null使用原始高度）
     * @param maxFrames 最大帧数（可为null不限制）
     * @throws IOException 处理异常
     */
    public static void gifToBin(String gifPath, String outputBin, Integer width, Integer height, Integer maxFrames)
            throws IOException {

        // 获取二进制数据
        byte[] binData = gifToBinData(gifPath, width, height, maxFrames);

        // 写入文件
        try (FileOutputStream fos = new FileOutputStream(outputBin)) {
            fos.write(binData);
        }

        System.out.println("转换完成 → " + outputBin);
        File outputFile = new File(outputBin);
        System.out.printf("文件大小: %.2f KB%n", outputFile.length() / 1024.0);
    }

    public static void main(String[] args) {
        Options options = new Options();
        
        options.addOption("w", "width", true, "输出宽度（像素）");
        options.addOption("h", "height", true, "输出高度（像素）");
        options.addOption("m", "max-frames", true, "最大帧数（可选）");
        options.addOption(null, "help", false, "显示帮助信息");

        CommandLineParser parser = new DefaultParser();
        
        try {
            CommandLine cmd = parser.parse(options, args);
            
            if (cmd.hasOption("help") || cmd.getArgs().length == 0) {
                HelpFormatter formatter = new HelpFormatter();
                formatter.printHelp("java -jar gif2bin.jar <input.gif> [options]", 
                    "将GIF转为RGB565格式的bin文件", options, "");
                return;
            }

            String inputPath = cmd.getArgs()[0];
            Integer width = cmd.hasOption("width") ? Integer.parseInt(cmd.getOptionValue("width")) : null;
            Integer height = cmd.hasOption("height") ? Integer.parseInt(cmd.getOptionValue("height")) : null;
            Integer maxFrames = cmd.hasOption("max-frames") ? Integer.parseInt(cmd.getOptionValue("max-frames")) : null;

            Path inputFile = Paths.get(inputPath);
            String outputBin = inputFile.getFileName().toString().replaceFirst("\\.[^.]+$", ".bin");

            gifToBin(inputPath, outputBin, width, height, maxFrames);

        } catch (ParseException e) {
            System.err.println("参数解析错误: " + e.getMessage());
            HelpFormatter formatter = new HelpFormatter();
            formatter.printHelp("java -jar gif2bin.jar <input.gif> [options]", options);
        } catch (IOException e) {
            System.err.println("文件处理错误: " + e.getMessage());
            System.err.println("请检查:");
            System.err.println("1. 输入文件是否存在且可读");
            System.err.println("2. 输入文件是否为有效的GIF格式");
            System.err.println("3. 输出目录是否有写入权限");
            System.err.println("4. 某些复杂的GIF文件可能需要更新的Java版本或使用其他工具预处理");
            System.err.println("建议: 可以尝试用其他工具(如GIMP、Photoshop)重新保存GIF文件后再转换");
        } catch (Exception e) {
            System.err.println("处理过程中发生错误: " + e.getMessage());
            System.err.println("错误类型: " + e.getClass().getSimpleName());
            if (e instanceof ArrayIndexOutOfBoundsException) {
                System.err.println("这通常是由于GIF文件格式问题导致的。");
                System.err.println("建议尝试:");
                System.err.println("1. 使用其他工具重新保存GIF文件");
                System.err.println("2. 检查GIF文件是否损坏");
            }
            e.printStackTrace();
        }
    }
}
