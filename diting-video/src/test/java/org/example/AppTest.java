package org.example;

import org.junit.Test;

import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Unit test for simple App.
 */
public class AppTest 
{
    /**
     * Rigorous Test :-)
     */
    @Test
    public void shouldAnswerWithTrue()
    {
        String s = "acac";
        String s2 = s + s;


        int i = s2.indexOf(s, 1);

        String s3 = s2.substring(1, s2.length() - 1);

        boolean res = s3.contains(s);
        System.out.println("ab".contains("ab"));
    }
    
    
    @Test
    public void test_01() {
        List<List<String>> lists = Arrays.asList(
                Collections.singletonList("a"),
                Collections.singletonList("b")
        );
        System.out.println(lists);


        System.out.println(
        lists.stream()
                .flatMap(Collection::stream)
                .collect(Collectors.toList()));
    }
}
