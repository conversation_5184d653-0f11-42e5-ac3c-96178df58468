package org.lab.emqx.controller;

import jakarta.annotation.Resource;
import org.dromara.mica.mqtt.codec.MqttQoS;
import org.dromara.mica.mqtt.spring.client.MqttClientTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.nio.charset.StandardCharsets;

@RestController
public class EmqxController {
    @Resource
    private MqttClientTemplate client;

    @GetMapping("send")
    public void send(@RequestParam String deviceNo, @RequestParam String msg) {
        client.publish(String.format("/test/%s/client", deviceNo), msg.getBytes(StandardCharsets.UTF_8), MqttQoS.QOS1);
    }
}
