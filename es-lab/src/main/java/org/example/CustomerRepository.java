package org.example;

import org.springframework.data.elasticsearch.annotations.Query;
import org.springframework.data.elasticsearch.repository.ElasticsearchRepository;

import java.util.List;

public interface CustomerRepository extends ElasticsearchRepository<Customer, String> {

	Customer findByFirstName(String firstName);

	List<Customer> findByLastName(String lastName);

	@Query("{\n" +
			// "\t\"query_string\": {\n" +
			"\t  \"query\": \"Select * from customer where firstName = 'Bob'\"\n" +
			// "\t}\n" +
			"}")
	List<Customer> all();
}