package org.example;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.http.util.EntityUtils;
import org.elasticsearch.client.Response;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;

/**
 * Created by she<PERSON><PERSON><PERSON> on 2018/11/20.
 */
@RestController
@RequestMapping("/es")
public class SimpleController {

    private static final String QUERY_PARAMETER_REST_TOTAL_HITS_AS_INT = "rest_total_hits_as_int";
    private static final String QUERY_PARAMETER_IGNORE_THROTTLED = "ignore_throttled";

    @Autowired
    private CustomerRepository repository;

    @Autowired
    private ElasticsearchRestTemplate elasticsearchRestTemplate;

    @Resource
    private ElasticsearchOperations operations;

    // @Autowired
    // private RestClient restClient;

    @RequestMapping("/indexExists/{indexName}")
    public Boolean indexExists(@PathVariable String indexName) {
        return elasticsearchRestTemplate.indexOps(IndexCoordinates.of(indexName)).exists();
    }

    @RequestMapping("/save")
    public String testEsRepo() {
        saveCustomers();
        return "OK";
    }

    @RequestMapping("/fetchAll")
    public Iterable<Customer> fetchAll() {
        return this.repository.findAll();
    }

    @RequestMapping("/findByFirstName")
    public Customer findByFirstName() {
        return this.repository.findByFirstName("Alice");
    }

    @RequestMapping("/findByLastName")
    public List<Customer> findByLastName() {
        return this.repository.findByLastName("Smith");
    }

    @RequestMapping("/nativeQuery")
    public void nativeQueryC() {
        this.nativeQuery();
    }

    @RequestMapping("/all")
    public void all() {
        this.repository.all();
    }

    // @PostMapping("/{indices}/_search")
    // public Object search(@PathVariable String indices,
    //                      @RequestBody(required = false) JSONObject query) {
    //     StringBuilder endpoint = new StringBuilder("/").append(indices).append("/_search");
    //     Request request = new Request(HttpMethod.POST.name(), endpoint.toString());
    //     request.addParameter(QUERY_PARAMETER_REST_TOTAL_HITS_AS_INT, Boolean.TRUE.toString());
    //     request.addParameter(QUERY_PARAMETER_IGNORE_THROTTLED, Boolean.TRUE.toString());
    //     try {
    //         if (query != null) {
    //             request.setJsonEntity(query.toString());
    //         }
    //         Response response = restClient.performRequest(request);
    //         return responseToJSONObject(response);
    //     } catch (IOException e) {
    //         e.printStackTrace();
    //         return "error";
    //     }
    // }

    private JSONObject responseToJSONObject(Response response) throws IOException {
        String body = EntityUtils.toString(response.getEntity());
        return JSON.parseObject(body);
    }

    private void saveCustomers() {
        this.repository.deleteAll();
        this.repository.save(new Customer("Alice", "Smith"));
        this.repository.save(new Customer("Bob", "Smith"));
    }

    private void nativeQuery() {
        // 多条件查询
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();

        queryBuilder.must(QueryBuilders.matchQuery("firstName", "Alice"));

        NativeSearchQuery query = new NativeSearchQueryBuilder()
                .withQuery(queryBuilder)
                // .withPageable(PageRequest.of(0, 1))
                .build();


        SearchHits<Customer> search = operations.search(query, Customer.class);

    }

}
