package org.example;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ListUtils;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStreamWriter;
import java.util.List;
import java.util.concurrent.atomic.LongAdder;

/**
 * Unit test for simple App.
 */
@Slf4j
public class AppTest {

    final String sql_template = "UPDATE express_waybill SET meterage_weight=%s, gross_weight=%s, order_service=%s, ego_type=1 WHERE waybill_no='%s' AND ego_type!=1; \r\n";
    // final String sql_template = "UPDATE express_waybill SET meterage_weight=%s, order_service=%s, ego_type=1 WHERE waybill_no='%s' AND ego_type!=1; \r\n";
    final String order_service_template = "'[{\"name\": \"%s\", \"type\": \"1\", \"value\": %s}]'";

    @org.junit.Test
    public void read() {
        LongAdder total = new LongAdder();
        String fileName = "/Users/<USER>/IdeaProjects/laboratory/excel-processor/src/test/java/org/example/data";

        File[] files = new File(fileName).listFiles();

        for (File file : files) {
            String fn = file.getName();
            String path = file.getPath();

            // 这里 需要指定读用哪个class去读，然后读取第一个sheet 文件流会自动关闭
            EasyExcel.read(path, ExcelData.class, new ReadListener<ExcelData>() {
                /**
                 * 单次缓存的数据量
                 */
                public static final int BATCH_COUNT = 2;
                /**
                 *临时存储
                 */
                private List<ExcelData> cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

                @Override
                public void invoke(ExcelData data, AnalysisContext context) {
                    cachedDataList.add(data);
                    if (cachedDataList.size() >= BATCH_COUNT) {
                        saveData();
                        // 存储完成清理 list
                        cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
                    }
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    saveData();
                }

                /**
                 * 加上存储数据库
                 */
                private void saveData() {
                    StringBuilder sb = new StringBuilder();
                    cachedDataList.forEach(f -> {
                        String serv = String.format(order_service_template, f.getAdded_service(), f.getCost());
                        String res = String.format(sql_template, f.getMeterage_weight(), f.getGross_weight(), serv, f.getWaybill_no());
                        sb.append(res);
                    });
                    writeFile(sb.toString(), "/Users/<USER>/IdeaProjects/laboratory/excel-processor/src/test/java/org/example/result/" + fn.split("\\.")[0] +".sql");
                    total.add(cachedDataList.size());
                }
            }).sheet()
                    .headRowNumber(9)
                    .doRead();
            log.info("{}条数据，处理成功！", total.intValue());
        }


    }


    public static void writeFile(String content, String fileName) {
        File file = new File(fileName);
        try (FileOutputStream fos = new FileOutputStream(file, true);
             OutputStreamWriter osw = new OutputStreamWriter(fos, "utf-8");) {
            // 写入内容
            osw.write(content);
            // 换行
            // osw.write("\r\n");
        } catch (Exception e) {
            log.error("写入文件发生异常", e);
        }
    }

}
