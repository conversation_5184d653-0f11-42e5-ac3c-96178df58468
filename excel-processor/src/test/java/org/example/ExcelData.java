package org.example;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <p>文件名称:org.example.ExcelData</p>
 * <p>文件描述: </p>
 * <p>版权所有: Copyright(C)2019-2022</p>
 * <p>公司: 趣店集团 </p>
 * <p>内容摘要: </p>
 * <p>其他说明: </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen</a>
 * @version 1.0
 * @since 2022/8/3
 */
@Data
public class ExcelData {

    @ExcelProperty("计费重量")
    private String meterage_weight;
    @ExcelProperty("实际重量")
    private String gross_weight;
// [{"name": "运费", "type": "1", "value": 9.2}]

    @ExcelProperty("增值费用")
    private String added_service;

    @ExcelProperty("费用(元)")
    private String cost;

    private String order_service;
    @ExcelProperty("运单号码")
    private String waybill_no;
}
